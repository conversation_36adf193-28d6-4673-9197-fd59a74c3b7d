from typing import List

from fastapi import Request

from app.schemas.user_schema import UserEntity
from app.service.user_service import login_service, register_service, find_user_roles, \
    find_user_permission, add_role, \
    check_permission
# 移除直接的数据库模型依赖
from app.utils.oauth2_helper import jwt_manager
from app.utils.response_utils import ResponseUtils


def login(data: UserEntity, request: Request):
    result = login_service(data)
    # 现在service返回的是实际的业务处理结果
    if result and result.get('success'):
        # 登录成功，生成访问令牌和刷新令牌
        user_data = result.get('data', {})
        access_token = jwt_manager.create_access_token(user_data)
        refresh_token = jwt_manager.create_refresh_token(user_data)
        return ResponseUtils.success("登录成功", data={
            "access_token": access_token,
            "refresh_token": refresh_token,
            "user_info": user_data
        })
    else:
        # 登录失败
        message = result.get('message', '登录失败') if result else '登录失败'
        return ResponseUtils.failed(message)


def register(data: UserEntity):
    result = register_service(data)
    # 现在service返回的是实际的业务处理结果
    if result and result.get('success'):
        return ResponseUtils.success(result.get('message', '注册成功'))
    else:
        message = result.get('message', '注册失败') if result else '注册失败'
        return ResponseUtils.failed(message)


def get_roles(user_id: int):
    result = find_user_roles(user_id)
    if result and result.get('success'):
        return ResponseUtils.success(result.get('message', '获取角色成功'), data=result.get('data'))
    else:
        message = result.get('message', '获取角色失败') if result else '获取角色失败'
        return ResponseUtils.failed(message)


def get_permissions(user_id: int):
    result = find_user_permission(user_id)
    if result and result.get('success'):
        return ResponseUtils.success(result.get('message', '获取权限成功'), data=result.get('data'))
    else:
        message = result.get('message', '获取权限失败') if result else '获取权限失败'
        return ResponseUtils.failed(message)


def change_user_roles(user_id: int, role_ids: List[int]):
    result = add_role(user_id, role_ids)
    if result and result.get('success'):
        return ResponseUtils.success(result.get('message', '修改角色成功'))
    else:
        message = result.get('message', '修改角色失败') if result else '修改角色失败'
        return ResponseUtils.failed(message)


def check_permission1(user_id: int, permission_name: str):
    result = check_permission(user_id, permission_name)
    if result and result.get('success'):
        return ResponseUtils.success(result.get('message', '权限检查完成'), data=result.get('data'))
    else:
        message = result.get('message', '权限检查失败') if result else '权限检查失败'
        return ResponseUtils.failed(message)

"""
任务发布工具类
提供统一的任务发布接口
"""
from typing import Dict, Any, List, Optional

from app.tasks.proxy import execute


class TaskPublisher:
    """任务发布器"""

    @classmethod
    def publish_task(cls, operation: str, args: Optional[List] = None,
                     kwargs: Optional[Dict] = None,
                     wait_for_result: bool = True, timeout: int = 30) -> Dict[str, Any]:
        """
        发布任务到Celery队列

        Args:
            operation: 操作路径，如 'user.login', 'user.register' 等
            args: 位置参数列表
            kwargs: 关键字参数字典
            wait_for_result: 是否等待任务执行完成
            timeout: 等待超时时间（秒）

        Returns:
            Dict: 任务执行结果或状态信息
        """
        if args is None:
            args = []
        if kwargs is None:
            kwargs = {}

        # 使用apply_async调用代理任务，传递位置参数
        result = execute.apply_async([operation, args, kwargs])

        print("result:", result)

        if wait_for_result:
            try:
                # 等待任务执行完成并获取结果
                task_result = result.get(timeout=timeout)
                return task_result
            except Exception as e:
                return {
                    'success': False,
                    'message': f'任务执行超时或失败: {str(e)}',
                    'task_id': result.id
                }
        else:
            # 不等待结果，直接返回任务信息
            return {
                'task_id': result.id,
                'status': 'pending',
                'message': f'{operation}请求已提交，正在处理中'
            }

    # 添加一个通用的方法，其他模块的方法不变
    @classmethod
    def publish_module_task(cls, module: str, operation: str, *args, wait_for_result: bool = True,
                            timeout: int = 30, **kwargs) -> Dict[str, Any]:
        """
        发布指定模块的任务（通用方法）

        Args:
            module: 模块名称，如 'user', 'role', 'permission' 等
            operation: 操作名称
            *args: 位置参数
            wait_for_result: 是否等待任务执行完成
            timeout: 等待超时时间（秒）
            **kwargs: 关键字参数

        Returns:
            Dict: 任务执行结果或状态信息
        """
        print("test publish module task")
        return cls.publish_task(f'{module}.{operation}', list(args), kwargs, wait_for_result,
                                timeout)

    @classmethod
    def publish_user_task(cls, operation: str, *args, wait_for_result: bool = True,
                          timeout: int = 30, **kwargs) -> Dict[str, Any]:
        """发布用户相关任务"""
        return cls.publish_task(f'user.{operation}', list(args), kwargs, wait_for_result, timeout)

    @classmethod
    def publish_role_task(cls, operation: str, *args, wait_for_result: bool = True,
                          timeout: int = 30, **kwargs) -> Dict[str, Any]:
        """发布角色相关任务"""
        return cls.publish_task(f'role.{operation}', list(args), kwargs, wait_for_result, timeout)

    @classmethod
    def publish_permission_task(cls, operation: str, *args, wait_for_result: bool = True,
                                timeout: int = 30, **kwargs) -> Dict[str, Any]:
        """发布权限相关任务"""
        return cls.publish_task(f'permission.{operation}', list(args), kwargs, wait_for_result,
                                timeout)

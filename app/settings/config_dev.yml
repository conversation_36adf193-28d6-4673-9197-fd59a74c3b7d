app_env: development

debug: true

mysql:
  host: **************
  port: 3306
  db: yongzhou_db
  user: root
  password: xuchaobo
  url: mysql://localhost

redis:
  host: **************
  port: 6379
  db: 1
  password: "123456"
  url: redis://localhost:6379/1

jwt:
  # jwt签名密钥，生产环境下面必须强密钥
  secret_key: "pengxun"
  # 加密算法
  algorithm: "HS256"
  # 访问token的过期时间
  access_token_expire_minutes: 30
  # 刷新token的过期时间
  refresh_token_expire_days: 7
  whitelist_paths:
    - "/v1/user/login"
    - "/v1/user/register"
    - "/v1/user/refresh"
    - "/docs"
    - "/redoc"
    - "/openapi.json"
    - "/static"
    - "/favicon.ico"
    - "/v1/test/test"

celery:
  # Celery broker URL (使用Redis)
  broker_url: "redis://**************:6379/2"
  # Celery result backend (使用Redis)
  result_backend: "redis://**************:6379/3"
  # 任务序列化格式
  task_serializer: "json"
  # 接受的内容类型
  accept_content: [ "json" ]
  # 结果序列化格式
  result_serializer: "json"
  # 时区设置
  timezone: "Asia/Shanghai"
  # 是否启用UTC
  enable_utc: true



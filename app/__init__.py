from pathlib import Path

from fastapi import FastAPI, HTTPException
from fastapi.exceptions import RequestValidationError
from starlette.staticfiles import StaticFiles

from app.exception.exception_handler import global_exception_handler, http_exception_handler, \
    validation_exception_handler
from app.middleware.auth_middleware import create_auth_middleware
from app.router import all_routers

# 获取项目根目录的静态文件路径
BASE_DIR = Path(__file__).parent.parent
static_dir = BASE_DIR / "static"


def create_app():
    # 创建app实例
    app = FastAPI()

    # 注册全局异常处理器
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, global_exception_handler)

    # 中间件,要放在全局异常处理后面
    app.add_middleware(create_auth_middleware)

    # 加载静态文件
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

    # 加载子路由 /v1/user/login
    for router in all_routers:
        app.include_router(router, prefix="/v1")
    # 如果你子路由没有整合在一起，可以一个一个加载

    return app

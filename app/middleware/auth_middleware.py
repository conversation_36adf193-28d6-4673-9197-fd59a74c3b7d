from fastapi import HTTPException
from starlette import status
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import JSONResponse

from app.settings.config import settings
from app.utils.oauth2_helper import global_auth_middleware


# 设计这个类，实际上就是http的拦截器（中间件）
class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        self.whitelist_paths = settings.jwt.whitelist_paths

    # 这个函数需要重新，每次请求都要进入这个函数
    # request：获取请求对象
    # call_next：是否继续用访问后面的业务
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        try:
            # 先检查是否在白名单里面
            if self._is_whitelisted(request.url.path):
                # 代表将请求传递目标地址，request对象传递过去
                # 加上日志,ip time 哪个接口
                print(f"白名单接口:{request.url.path}")
                response = await call_next(request)
                print(1111111111111111)
                print(response)
                return response
            # 验证token
            user = await global_auth_middleware.verify_token_from_request(request)
            # 将user的信息放在请求头,user/add
            request.state.user = user
            # 继续处理响应
            response = await call_next(request)
            return response
        except HTTPException as e:
            return JSONResponse(status_code=e.status_code, content={"detail": e.detail},
                                headers=e.headers)
        except Exception as e:
            return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                content={"detail": e})

    # 判断是否在白名单里面
    def _is_whitelisted(self, path: str) -> bool:
        if path in self.whitelist_paths:
            return True
        # 检查前缀是否匹配
        for white_path in self.whitelist_paths:
            if white_path.endswith("*"):
                prefix = white_path[:-1]
                if path.startswith(prefix):
                    return True
        return False


# 中间件的工厂函数
def create_auth_middleware(app):
    return AuthMiddleware(app)
